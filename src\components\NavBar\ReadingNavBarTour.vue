<template>
  <div class="tour-container">
    <div ref="actionAreaTop" class="action-area-top"></div>
    <div
      class="image-container"
      @click="nextImage"
      :class="{
        'position-top': currentStep === 0,
        'position-bottom': currentStep === 1,
      }"
    >
      <img
        :src="currentImage"
        :alt="`Tour step ${currentStep + 1}`"
        class="tour-image"
      />
      <div class="step-indicator">
        {{ currentStep + 1 }} / {{ tourImages.length }}
      </div>
    </div>
    <div ref="actionAreaBottom" class="action-area-bottom"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from "vue";
import { ElNotification } from "element-plus";

import Step1Img from "@/components/NavBar/Assets/Tour/1.点击顶部弹出导航栏带下一步.png";
import Step2Img from "@/components/NavBar/Assets/Tour/2.底部导航栏下一步.png";
import Step3Img from "@/components/NavBar/Assets/Tour/3.单指翻页.png";
import Step4Img from "@/components/NavBar/Assets/Tour/4.手写笔设置.png";
import Step5Img from "@/components/NavBar/Assets/Tour/5.选词做笔记.png";
import Step6Img from "@/components/NavBar/Assets/Tour/6.查看笔记.png";

const emit = defineEmits(["tour-end"]);

// 图片数组
const tourImages = [Step1Img, Step2Img, Step3Img, Step4Img, Step5Img, Step6Img];

// 当前步骤索引
const currentStep = ref(0);

// 当前显示的图片
const currentImage = computed(() => tourImages[currentStep.value]);

// 点击图片切换到下一张
const nextImage = () => {
  if (currentStep.value < tourImages.length - 1) {
    // 还有下一张图片，切换到下一张
    currentStep.value++;
  } else {
    // 已经是最后一张，发送 tour-end 事件
    emit("tour-end");
  }
};

onMounted(() => ElNotification.closeAll());
</script>

<style scoped>
.tour-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
}

.image-container {
  position: absolute;
  cursor: pointer;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Step1 图片吸附在 actionAreaTop 下面 */
.image-container.position-top {
  top: 30px; /* actionAreaTop 的高度 */
}

/* Step2 图片吸附在 actionAreaBottom 上面 */
.image-container.position-bottom {
  bottom: 30px; /* actionAreaBottom 的高度 */
  transform: translateY(-100%);
}

/* 其他图片居中显示 */
.image-container:not(.position-top):not(.position-bottom) {
  top: 50%;
  transform: translateY(-50%);
}

.tour-image {
  width: 100%; /* 最大宽度100% */
  height: auto; /* 高度自适应 */
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.step-indicator {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.action-area-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 30px;
  z-index: 10000;
}

.action-area-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  z-index: 10000;
}
</style>
